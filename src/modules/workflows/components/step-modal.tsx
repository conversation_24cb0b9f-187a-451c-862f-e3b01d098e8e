"use client";

import "react-image-crop/dist/ReactCrop.css";
import "react-image-crop/src/ReactCrop.scss";

import { Button, Radio } from "antd";

import { ModalCommon } from "@/components/common/modal-common";
export interface IMProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  setItems: (value: any) => void;
  //   fetchListWorkflow: () => void;
  setValue: (value: any) => void;
  value: any;
  onClickCreateStep: any;
}

export function ModalStep(props: IMProps) {
  const { modalState, setModalState, setValue, value, onClickCreateStep } =
    props;
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={600}
      closable={false}
    >
      {/* <CropImageCommon /> */}
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-1">
          <p className="font-medium">Source Type</p>
          <Radio.Group
            onChange={(e) => {
              setValue({ ...value, sourceType: e.target.value });
            }}
            value={value.sourceType}
          >
            <Radio value={"FastGeo Model"}>FastGeo Model</Radio>
            <Radio value={"External Model"}>External Model</Radio>
            <Radio value={"Tools"}>Tools</Radio>
            <Radio value={"Code"}>Code</Radio>
          </Radio.Group>
        </div>
        <div className="flex flex-col gap-1">
          <p className="font-medium">Source Data</p>
          <Radio.Group
            onChange={(e) => {
              setValue({ ...value, sourceData: e.target.value });
            }}
            value={value.sourceData}
          >
            <Radio value={"Original"}>Step 1</Radio>
            <Radio value={"Cropped"}>Cropped</Radio>
          </Radio.Group>
        </div>
        <Button type="primary" onClick={onClickCreateStep}>
          Create step
        </Button>
      </div>
    </ModalCommon>
  );
}
