import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";

import { ButtonCommon } from "@/components/common/button-common";
import ColorPickerCommon from "@/components/common/color-picker";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { ToogleCommon } from "@/components/common/toogle-common";

import { useCreateWorkflow } from "../hooks/useCreateWorkflow.hook";
import { useDeleteWorkflow } from "../hooks/useDeleteWorkflow.hook";
import { useUpdateWorkflow } from "../hooks/useUpdateWorkflow.hook";
import {
  WorkflowsBody,
  WorkflowsBodyType,
} from "../model/schema/workflows.schema";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  fetchListWorkflow: () => void;
}

export function ModalWorkflow(props: IModalCompanyProps) {
  const { modalState, setModalState, fetchListWorkflow } = props;
  const { request: requestCreateWorkflow, loading: loadingCreate } =
    useCreateWorkflow();
  const { request: requestUpdateWorkflow, loading: loadingUpdate } =
    useUpdateWorkflow();
  const { request: requestDeleteWorkflow, loading: loadingDelete } =
    useDeleteWorkflow();
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };

  const { control, handleSubmit, setValue, getValues } =
    useForm<WorkflowsBodyType>({
      resolver: zodResolver(WorkflowsBody),
      defaultValues: {
        name: modalState.detailInfo?.name || "",
        buttonName: modalState.detailInfo?.buttonName || "",
        backgroundColor: modalState.detailInfo?.backgroundColor ?? "#000000",
        color: modalState.detailInfo?.color ?? "#FFFFFF",
        isActive: modalState.detailInfo?.isActive ?? true,
      },
    });

  const isConfirm = modalState.type === "delete";

  const onSubmit = (values: WorkflowsBodyType) => {
    if (modalState.type === "create") {
      requestCreateWorkflow(
        {
          ...values,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create workflow successfully");
          fetchListWorkflow();
        },
      );
    }
    if (modalState.type === "update") {
      requestUpdateWorkflow(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update workflow successfully");
          fetchListWorkflow();
        },
      );
    }
  };

  const handleDelete = () => {
    requestDeleteWorkflow(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        fetchListWorkflow();
      },
    );
  };
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this workflows?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            workflow
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDelete}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === "update" ? "Update workflow" : "Add workflow"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type workflow name here"
              control={control}
              isRequired={true}
            />
            <InputTextCommon
              label="Button Name"
              name="buttonName"
              placeholder="Type button name here"
              control={control}
              isRequired={true}
            />
            <ColorPickerCommon
              getValues={getValues}
              setValue={setValue}
              control={control}
              name="backgroundColor"
              label="Background Color"
              placeholder="Choose background color here"
              isRequired={true}
            />

            <ColorPickerCommon
              setValue={setValue}
              getValues={getValues}
              control={control}
              name="color"
              label="Color"
              placeholder="Choose color here"
              isRequired={true}
            />
            <ToogleCommon label="Active" control={control} name="isActive" />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                loading={loadingCreate || loadingUpdate || loadingDelete}
                type="submit"
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === "update"
                  ? "Update workflow"
                  : "Add workflow"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
