/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ToolOutlined,
} from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import { type TableColumnsType, Tag } from "antd";
import { debounce } from "lodash";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";

import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { IPaginationResponse } from "@/common/interfaces/response/IPaginationResponse";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";

import { selectWorkflow } from "../redux";
import { getWorkflows } from "../redux/thunks";
import { ModalWorkflow } from "./modal-workflows";

const TableWorkflows: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const workflows = useAppSelector(selectWorkflow);
  const pathname = usePathname();
  const { replace } = useRouter();
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });
  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: workflows?.result?.items ?? [],
      reduxTablePagination: {
        total: workflows?.result?.pagination.total ?? 0,
      } as IPaginationResponse,
      requestState: workflows?.status ?? RequestState.idle,
      getDataAction: getWorkflows,
      filter: {
        page: 1,
        pageSize: 10,
      },
    });

  //table
  const columns: TableColumnsType<any> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
            <ToolOutlined
              onClick={() => router.push(`/workflows-management/${record.id}`)}
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },
    {
      title: "Id",
      dataIndex: "id",
      key: "id",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render(value, index) {
        return <p key={index}>{value}</p>;
      },
    },

    {
      title: "Button Name",
      dataIndex: "buttonName",
      key: "buttonName",
      render(value, record, index) {
        return (
          <Tag
            style={{
              color: record.color,
              backgroundColor: record.backgroundColor,
            }}
            key={index}
          >
            {value}
          </Tag>
        );
      },
    },
    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      render: (status) =>
        status ? (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
          >
            Active
          </Tag>
        ) : (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Inactive
          </Tag>
        ),
    },
  ];

  const buttons = [
    {
      title: "Active",
      isActveString: "true",
      onclick: () => {
        //set page to 1
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "true");
        params.set("page", "1");
        replace(`${pathname}?${params.toString()}`);
      },
    },
    {
      title: "Inactive",
      isActveString: "false",
      onclick: () => {
        //set page to 1
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "false");
        params.set("page", "1");
        replace(`${pathname}?${params.toString()}`);
      },
    },
    {
      title: "All",
      isActveString: undefined,
      onclick: () => {
        //set page to 1
        const params = new URLSearchParams(searchParams);
        params.set("page", "1");
        params.delete("isActive");
        replace(`${pathname}?${params.toString()}`);
      },
    },
  ];
  const updateSearchParams = useCallback(
    debounce((keyword) => {
      const params = new URLSearchParams(queries);
      if (keyword) {
        params.set("keyword", keyword);
      } else {
        params.delete("keyword");
      }
      params.set("page", "1");
      router.replace(`${window.location.pathname}?${params.toString()}`);
    }, 300),
    [queries, router],
  );
  useEffect(() => {
    if (workflows.result?.items.length === 0) {
      if (tablePagination.current && tablePagination.current - 1 > 0) {
        refresh({
          page: tablePagination.current - 1,
        });
        const params = new URLSearchParams(queries);
        params.set("page", (tablePagination.current - 1).toString());
        router.replace(`${window.location.pathname}?${params.toString()}`);
      }
    }
  }, [workflows.result?.items]);
  return (
    <>
      {modalState.isOpen && (
        <ModalWorkflow
          fetchListWorkflow={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold">Workflows</p>
        <hr />
        <div className="">
          <div className="flex justify-between gap-2">
            <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[400px] bg-white border">
              <IconSearch />
              <input
                type="text"
                placeholder="Search"
                className="w-full font-normal  outline-none text-primary placeholder:text-gray80"
                onChange={(e) => {
                  updateSearchParams(e.target.value);
                }}
                defaultValue={queries.keyword}
              />
            </div>
            <div className="flex gap-2">
              {buttons.map((button, index) => {
                let className: string = "";
                const isActiveSearchParam = searchParams.get("isActive");
                if (isActiveSearchParam === null && button.title === "All") {
                  className = "btn-primary btn-active";
                }
                if (isActiveSearchParam === button.isActveString) {
                  className = "btn-primary btn-active";
                }
                return (
                  <button
                    key={index}
                    className={`btn btn-sm ${className}`}
                    onClick={button.onclick}
                  >
                    {button.title}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
        <TableCommon
          className="font-visby"
          pagination={tablePagination}
          loading={workflows.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={workflows?.result?.items}
          footer={() => (
            <div className="justify-center my-2 ">
              <button
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  })
                }
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  Add workflow
                </span>
              </button>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableWorkflows;
