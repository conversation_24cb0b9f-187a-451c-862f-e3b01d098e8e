import { useState } from "react";

import { RequestState } from "@/constants/general.const";

import stepWorkflowsRequest from "../api/step-workflow.api";
interface ResponseObject {
  id: number;
  name: string;
  workflowId: number;
  processType: number;
  modelId: number;
  toolType: number;
  polygonId: number;
  dataSourceType: number;
  dataValue: string;
  outputType: number;
  boundingBoxId: number;
  boundingRowsId: number;
}

export const useGetListStepWorkflow = () => {
  const [requestState, setRequestState] = useState<RequestState>(
    RequestState.initial,
  );
  const [data, setData] = useState<ResponseObject[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      keyword?: string;
      skipCount: number;
      maxResultCount: number;
      WorkFlowId: number;
    },
    onError?: Function,
  ) {
    setLoading(true);
    setRequestState(RequestState.request);
    const response = await stepWorkflowsRequest.getAll(params);
    if (response?.status === 200) {
      setTotal(response.data?.result?.totalCount);
      setData(response.data.result?.items);
      setRequestState(RequestState.success);
      setLoading(false);
    } else {
      onError?.(response);
      setRequestState(RequestState.error);
      setLoading(false);
    }
  }
  return {
    request,
    requestState,
    data,
    total,
    loading,
  };
};
