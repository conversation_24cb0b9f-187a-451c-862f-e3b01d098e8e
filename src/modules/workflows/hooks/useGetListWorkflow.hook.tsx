import { useState } from "react";

import { RequestState } from "@/constants/general.const";

import workflowsApiRequest from "../api/workflows.api";

export const useGetListWorkflows = () => {
  const [requestState, setRequestState] = useState<RequestState>(
    RequestState.initial,
  );
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      keyword?: string;
      skipCount: number;
      maxResultCount: number;
      isActive?: boolean;
      sortField?: string;
      sortOrder?: string;
    },
    onError?: Function,
    onSuccess?: Function,
  ) {
    setLoading(true);
    setRequestState(RequestState.request);
    const response = await workflowsApiRequest.getListWorkflows(params);
    if (response?.state === "success") {
      setTotal(response.data?.pagination.total);
      setData(response.data?.items);
      setRequestState(RequestState.success);
      setLoading(false);
      onSuccess?.(response);
    } else {
      onError?.(response);
      setRequestState(RequestState.error);
      setLoading(false);
    }
  }
  return {
    request,
    requestState,
    data,
    total,
    loading,
  };
};
