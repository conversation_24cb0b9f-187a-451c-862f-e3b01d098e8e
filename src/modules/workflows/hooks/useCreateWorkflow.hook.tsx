import { useState } from "react";

import workflowsApiRequest from "../api/workflows.api";
import { WorkflowsBodyType } from "../model/schema/workflows.schema";

export const useCreateWorkflow = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: WorkflowsBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await workflowsApiRequest.createWorkflows({
      ...params,
      stepIds: params.steps || [],
    });
    if (response.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response.message);
      setLoading(false);
    }
  }
  return { request, loading };
};
