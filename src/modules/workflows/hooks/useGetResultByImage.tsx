import { useState } from "react";

import { RequestState } from "@/constants/general.const";

import stepWorkflowsRequest from "../api/step-workflow.api";

export const useGetResultByImage = () => {
  const [requestState, setRequestState] = useState<RequestState>(
    RequestState.initial,
  );
  const [data, setData] = useState<any>();
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      ImageId: any;
      StepId: any;
    },
    onError?: Function,
  ) {
    setLoading(true);
    setRequestState(RequestState.request);
    const response = await stepWorkflowsRequest.getResultStep(params);
    if (response?.status === 200) {
      setData(response?.data?.result);
      setRequestState(RequestState.success);
      setLoading(false);
    } else {
      onError?.(response);
      setRequestState(RequestState.error);
      setLoading(false);
    }
  }
  return {
    request,
    requestState,
    data,
    loading,
  };
};
