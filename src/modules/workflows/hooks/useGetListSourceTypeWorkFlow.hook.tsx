import { useState } from "react";

import { RequestState } from "@/constants/general.const";
import sourceTypeWorkflowsRequest from "@/modules/process-images/api/source-type.api";

export const useGetListSourceTypeWorkflows = () => {
  const [requestState, setRequestState] = useState<RequestState>(
    RequestState.initial,
  );
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      keyword?: string;
      skipCount: number;
      maxResultCount: number;
    },
    onError?: Function,
  ) {
    setLoading(true);
    setRequestState(RequestState.request);
    const response = await sourceTypeWorkflowsRequest.getAll(params);
    if (response?.status === 200) {
      setTotal(response.data?.result?.totalCount);
      setData(response.data.result?.items);
      setRequestState(RequestState.success);
      setLoading(false);
    } else {
      onError?.(response);
      setRequestState(RequestState.error);
      setLoading(false);
    }
  }
  return {
    request,
    requestState,
    data,
    total,
    loading,
  };
};
