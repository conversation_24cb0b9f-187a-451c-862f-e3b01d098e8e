import { useState } from "react";

import workflowsApiRequest from "../api/workflows.api";

export const useGetDetailWorkflow = () => {
  const [loading, setLoading] = useState(false);
  const [data] = useState<any>({});
  async function request(
    params: { id: string },
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await workflowsApiRequest.getDetailWorkflow(params);
    if (response?.state === "success") {
      onSuccess?.(response.data);
      setLoading(false);
    } else {
      onError?.(response.message);
      setLoading(false);
    }
  }

  return { request, data, loading };
};
