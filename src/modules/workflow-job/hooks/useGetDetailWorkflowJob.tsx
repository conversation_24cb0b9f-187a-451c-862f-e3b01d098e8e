import { useState } from "react";

import workflowsJobRequest from "../api/workflow-job.api";

export const useGetDetailWorkflowJob = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);

  const request = async (
    params: { Id: any },
    onSucess?: any,
    onError?: any,
  ) => {
    setLoading(true);
    const response = await workflowsJobRequest.getJobDetail(params);

    if (response.state === "success") {
      setLoading(false);
      onSucess?.(response.data);
      setData(response.data);
      return response.data;
    } else {
      setLoading(false);
      onError?.(response.message);
      return null;
    }
  };

  return { request, loading, data, setData };
};
