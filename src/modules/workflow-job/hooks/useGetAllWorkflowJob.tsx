import { useState } from "react";

import workflowsJobRequest from "../api/workflow-job.api";

export const useGetAllWorkflowJob = () => {
  const [total] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (params: {
    WorkflowId?: number;
    DrillholeId?: number;
    ImageId?: number;
    Status?: number;
    skipCount: number;
    maxResultCount: number;
  }) => {
    setLoading(true);
    const response = await workflowsJobRequest.getAll(params);
    if (response.state === "success") {
      setData(response.data?.items);
      setLoading(false);
      return response.data;
    } else {
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total, setData };
};
