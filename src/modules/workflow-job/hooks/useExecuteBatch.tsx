import { useState } from "react";
import { toast } from "react-toastify";

import { ProcessBatchBodyType } from "@/modules/process-batch-image/model/process-batch.schema";

import workflowsJobRequest from "../api/workflow-job.api";

export const useExecuteBatch = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ProcessBatchBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await workflowsJobRequest.executeBatch(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
    } else {
      onError?.(response.message);
      toast.error("Batch processing failed");
    }
    setLoading(false);
  }
  return { request, loading };
};
