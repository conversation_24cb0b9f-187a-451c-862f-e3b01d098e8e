import { useState } from "react";

import { ProcessBatchBodyType } from "@/modules/process-batch-image/model/process-batch.schema";

import workflowsJobRequest from "../api/workflow-job.api";

export const useUpdateJob = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ProcessBatchBodyType,
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await workflowsJobRequest.updateJob(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
    } else {
      onError?.(response);
    }
    setLoading(false);
  }
  return { request, loading };
};
