import { useState } from "react";

import workflowsJobRequest from "../api/workflow-job.api";

export const useRerunJob = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: {
      id: number;
    },
    onSuccess?: Function,
    onError?: Function,
  ) {
    setLoading(true);
    const response = await workflowsJobRequest.rerunJob(params);
    if (response.state === "success") {
      onSuccess?.(response.data);
    } else {
      onError?.(response);
    }
    setLoading(false);
  }
  return { request, loading };
};
