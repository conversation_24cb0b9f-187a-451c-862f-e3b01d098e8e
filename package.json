{"name": "quang-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_OPTIONS='--max-old-space-size=6144 --max-semi-space-size=512' next build", "build:dev": "NODE_OPTIONS='--max-old-space-size=6144 --max-semi-space-size=512' env-cmd -f .env.development next build", "build:staging": "NODE_OPTIONS='--max-old-space-size=6144 --max-semi-space-size=512' env-cmd -f .env.staging next build", "build:prod": "NODE_OPTIONS='--max-old-space-size=6144 --max-semi-space-size=512' env-cmd -f .env.production next build", "build:analyze": "ANALYZE=true NODE_OPTIONS='--max-old-space-size=6144' next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "eslint --ext .js,.jsx,.ts,.tsx src/", "lint:strict:fix": "eslint --ext .js,.jsx,.ts,.tsx src/ --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "code-quality": "npm run type-check && npm run lint:strict && npm run format:check"}, "dependencies": {"@ant-design/charts": "^2.2.6", "@ant-design/icons": "^5.3.7", "@ant-design/nextjs-registry": "^1.0.0", "@ant-design/plots": "^1.2.6", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@headlessui/react": "^1.7.18", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.4", "@microsoft/signalr": "^8.0.7", "@mui/material": "^6.1.1", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^8.0.0", "@reduxjs/toolkit": "^2.2.7", "@tabler/icons-react": "^3.11.0", "@tanstack/react-query": "^5.51.11", "@types/lodash": "^4.17.7", "@types/react-virtualized": "^9.22.2", "@types/three": "^0.174.0", "@types/uuid": "^10.0.0", "antd": "5.24.6", "antd-style": "3.7.1", "axios": "^1.6.8", "canvas": "^2.11.2", "clsx": "^2.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "idb": "^8.0.3", "inversify": "^6.0.2", "jotai": "^2.8.3", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "konva": "^9.3.11", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "next": "^14.2.24", "nextjs-progressbar": "^0.0.16", "query-string": "^9.0.0", "react": "^18", "react-arborist": "^3.4.3", "react-dom": "^18", "react-excel-renderer": "^1.1.0", "react-hook-form": "^7.51.5", "react-hook-form-antd": "^1.1.0", "react-icons": "^5.2.1", "react-icons-picker": "^1.0.9", "react-image-crop": "^11.0.5", "react-image-size": "^2.4.0", "react-konva": "^18.2.10", "react-konva-utils": "^1.0.6", "react-redux": "^9.1.2", "react-toastify": "^10.0.5", "react-virtualized": "9.22.6", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "reflect-metadata": "^0.2.2", "sass": "^1.77.6", "styled-components": "^6.1.8", "tailwind-merge": "^2.2.2", "three": "^0.174.0", "use-image": "^1.1.1", "uuid": "^10.0.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.3", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20", "@types/qs": "^6.9.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-highlight-words": "^0.16.7", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "autoprefixer": "^10.0.1", "daisyui": "^4.12.24", "eslint": "^8", "eslint-config-next": "^14.1.4", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8", "prettier": "^3.6.2", "tailwindcss": "^3.3.0", "typescript": "^5"}}